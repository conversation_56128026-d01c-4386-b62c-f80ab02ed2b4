<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LinkedIn Registration - GigGenius</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/bootstrap.min.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        .registration-choice-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            background-color: #fff;
        }
        .choice-card {
            border: 2px solid #eee;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .choice-card:hover {
            border-color: #0077B5;
            box-shadow: 0 5px 15px rgba(0, 119, 181, 0.2);
        }
        .choice-card.selected {
            border-color: #0077B5;
            background-color: rgba(0, 119, 181, 0.05);
        }
        .linkedin-profile-info {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .linkedin-logo {
            width: 80px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container registration-choice-container">
        <div class="text-center mb-4">
            <img src="{{ url_for('static', filename='img/linkedin-logo.png') }}" alt="LinkedIn" class="linkedin-logo">
            <h2>Complete Your Registration</h2>
            <p class="text-muted">Thanks for connecting with LinkedIn! Please choose how you'd like to join GigGenius.</p>
        </div>

        <div class="linkedin-profile-info">
            <h5>Your LinkedIn Profile</h5>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Name:</strong> {{ linkedin_data.first_name }} {{ linkedin_data.last_name }}</p>
                </div>
                <div class="col-md-6">
                    <p><strong>Email:</strong> {{ linkedin_data.email }}</p>
                </div>
            </div>
        </div>

        <form id="registrationForm" method="post" action="">
            <input type="hidden" name="linkedin_id" value="{{ linkedin_data.linkedin_id }}">
            <input type="hidden" name="first_name" value="{{ linkedin_data.first_name }}">
            <input type="hidden" name="last_name" value="{{ linkedin_data.last_name }}">
            <input type="hidden" name="email" value="{{ linkedin_data.email }}">
            
            <div class="row">
                <div class="col-md-6">
                    <div class="choice-card" id="geniusCard" onclick="selectUserType('genius')">
                        <h4>Join as a Genius</h4>
                        <p>I want to offer my skills and services to clients.</p>
                        <ul>
                            <li>Create a professional profile</li>
                            <li>Apply to jobs and projects</li>
                            <li>Get paid for your expertise</li>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="choice-card" id="clientCard" onclick="selectUserType('client')">
                        <h4>Join as a Client</h4>
                        <p>I want to hire talented professionals for my projects.</p>
                        <ul>
                            <li>Post jobs and projects</li>
                            <li>Find qualified experts</li>
                            <li>Manage work and payments</li>
                        </ul>
                    </div>
                </div>
            </div>

            <input type="hidden" name="user_type" id="userTypeInput" value="">
            
            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-lg" id="continueBtn" disabled>Continue</button>
                <p class="mt-3">
                    <a href="{{ url_for('landing_page') }}" class="text-muted">Cancel and return to home</a>
                </p>
            </div>
        </form>
    </div>

    <script src="{{ url_for('static', filename='js/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ url_for('static', filename='js/bootstrap.bundle.min.js') }}"></script>
    <script>
        function selectUserType(type) {
            // Reset both cards
            document.getElementById('geniusCard').classList.remove('selected');
            document.getElementById('clientCard').classList.remove('selected');
            
            // Select the clicked card
            if (type === 'genius') {
                document.getElementById('geniusCard').classList.add('selected');
                document.getElementById('userTypeInput').value = 'genius';
                document.getElementById('registrationForm').action = "{{ url_for('register_genius') }}";
            } else {
                document.getElementById('clientCard').classList.add('selected');
                document.getElementById('userTypeInput').value = 'client';
                document.getElementById('registrationForm').action = "{{ url_for('register_client') }}";
            }
            
            // Enable the continue button
            document.getElementById('continueBtn').disabled = false;
        }
    </script>
</body>
</html>
